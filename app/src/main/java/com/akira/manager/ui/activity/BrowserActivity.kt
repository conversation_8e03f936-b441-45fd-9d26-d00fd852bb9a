package com.akira.manager.ui.activity

import android.annotation.SuppressLint
import android.content.Context
import android.content.Intent
import android.content.pm.PackageManager
import android.util.Log
import android.graphics.Bitmap
import android.net.Uri
import android.os.Bundle
import android.view.KeyEvent
import android.view.View
import android.webkit.*
import android.net.http.SslError
import android.widget.Toast
import java.net.URLEncoder
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.activity.enableEdgeToEdge
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.KeyboardActions
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalSoftwareKeyboardController
import androidx.compose.ui.text.input.ImeAction
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.unit.dp
import androidx.compose.ui.viewinterop.AndroidView
import androidx.core.view.WindowCompat
import com.akira.manager.ui.theme.MDMTheme
import com.akira.manager.data.local.SettingsManager
import androidx.compose.foundation.isSystemInDarkTheme
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue

class BrowserActivity : ComponentActivity() {
    
    companion object {
        const val DEFAULT_HOME_URL = "https://www.google.com"
        const val EXTRA_URL = "extra_url"
    }
    
    private var webView: WebView? = null
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        
        WindowCompat.setDecorFitsSystemWindows(window, false)
        enableEdgeToEdge()
        
        val initialUrl = intent.getStringExtra(EXTRA_URL) ?: DEFAULT_HOME_URL
        
        setContent {
            val settingsManager = SettingsManager(this)
            val isAmoledDarkMode by settingsManager.isAmoledDarkMode.collectAsState(initial = false)
            val darkTheme = isSystemInDarkTheme()

            MDMTheme(
                darkTheme = darkTheme,
                amoledDarkMode = isAmoledDarkMode
            ) {
                BrowserScreen(
                    initialUrl = initialUrl,
                    onBackPressed = { finish() },
                    onWebViewCreated = { webView = it }
                )
            }
        }
    }
    
    override fun onKeyDown(keyCode: Int, event: KeyEvent?): Boolean {
        if (keyCode == KeyEvent.KEYCODE_BACK && webView?.canGoBack() == true) {
            webView?.goBack()
            return true
        }
        return super.onKeyDown(keyCode, event)
    }
    
    override fun onDestroy() {
        webView?.destroy()
        super.onDestroy()
    }
}

/**
 * Determines if the input text is a URL or a search term
 * Returns the appropriate URL to load
 */
private fun processUserInput(input: String): String {
    val trimmedInput = input.trim()

    return when {
        // Already has protocol
        trimmedInput.startsWith("http://") || trimmedInput.startsWith("https://") -> trimmedInput

        // Looks like a domain (contains dot and no spaces, and has valid domain pattern)
        trimmedInput.contains(".") && !trimmedInput.contains(" ") &&
        (trimmedInput.contains(".com") || trimmedInput.contains(".org") ||
         trimmedInput.contains(".net") || trimmedInput.contains(".edu") ||
         trimmedInput.contains(".gov") || trimmedInput.contains(".io") ||
         trimmedInput.contains(".co") || trimmedInput.contains(".uk") ||
         trimmedInput.contains(".de") || trimmedInput.contains(".fr") ||
         trimmedInput.contains(".ca") || trimmedInput.contains(".au") ||
         trimmedInput.contains(".in") || trimmedInput.contains(".jp") ||
         trimmedInput.matches(Regex(".*\\.[a-zA-Z]{2,}.*"))) -> "https://$trimmedInput"

        // Looks like localhost or IP
        trimmedInput.startsWith("localhost") ||
        trimmedInput.matches(Regex("^\\d+\\.\\d+\\.\\d+\\.\\d+(:\\d+)?$")) -> "http://$trimmedInput"

        // Everything else is a search term
        else -> "https://www.google.com/search?q=${URLEncoder.encode(trimmedInput, "UTF-8")}"
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun BrowserScreen(
    initialUrl: String,
    onBackPressed: () -> Unit,
    onWebViewCreated: (WebView) -> Unit
) {
    var currentUrl by remember { mutableStateOf(initialUrl) }
    var urlText by remember { mutableStateOf(initialUrl) }
    var isLoading by remember { mutableStateOf(false) }
    var loadingProgress by remember { mutableStateOf(0) }
    var canGoBack by remember { mutableStateOf(false) }
    var canGoForward by remember { mutableStateOf(false) }
    var webView by remember { mutableStateOf<WebView?>(null) }
    
    val context = LocalContext.current
    val keyboardController = LocalSoftwareKeyboardController.current
    
    Column(
        modifier = Modifier
            .fillMaxSize()
            .background(MaterialTheme.colorScheme.background)
    ) {
        // Top App Bar with navigation controls
        TopAppBar(
            title = {
                // Navigation buttons in the title area
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceEvenly,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    // Home button
                    IconButton(onClick = {
                        webView?.loadUrl(BrowserActivity.DEFAULT_HOME_URL)
                        urlText = BrowserActivity.DEFAULT_HOME_URL
                    }) {
                        Icon(
                            Icons.Default.Home,
                            contentDescription = "Home",
                            tint = MaterialTheme.colorScheme.primary
                        )
                    }

                    // Back button
                    IconButton(
                        onClick = { webView?.goBack() },
                        enabled = canGoBack
                    ) {
                        Icon(
                            Icons.Default.ArrowBack,
                            contentDescription = "Go Back",
                            tint = if (canGoBack) MaterialTheme.colorScheme.primary
                                  else MaterialTheme.colorScheme.onSurface.copy(alpha = 0.3f)
                        )
                    }

                    // Forward button
                    IconButton(
                        onClick = { webView?.goForward() },
                        enabled = canGoForward
                    ) {
                        Icon(
                            Icons.Default.ArrowForward,
                            contentDescription = "Go Forward",
                            tint = if (canGoForward) MaterialTheme.colorScheme.primary
                                  else MaterialTheme.colorScheme.onSurface.copy(alpha = 0.3f)
                        )
                    }

                    // Refresh/Stop button - show stop when loading, refresh when not loading
                    if (isLoading) {
                        // Stop button
                        IconButton(onClick = { webView?.stopLoading() }) {
                            Icon(
                                Icons.Default.Close,
                                contentDescription = "Stop Loading",
                                tint = MaterialTheme.colorScheme.primary
                            )
                        }
                    } else {
                        // Refresh button
                        IconButton(onClick = { webView?.reload() }) {
                            Icon(
                                Icons.Default.Refresh,
                                contentDescription = "Refresh",
                                tint = MaterialTheme.colorScheme.primary
                            )
                        }
                    }
                }
            },
            navigationIcon = {
                IconButton(onClick = onBackPressed) {
                    Icon(Icons.Default.ArrowBack, contentDescription = "Back to App")
                }
            },
            colors = TopAppBarDefaults.topAppBarColors(
                containerColor = MaterialTheme.colorScheme.surface,
                titleContentColor = MaterialTheme.colorScheme.onSurface
            )
        )
        
        // URL address bar
        OutlinedTextField(
            value = urlText,
            onValueChange = { urlText = it },
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = 16.dp),
            placeholder = { Text("Search or enter URL...") },
            singleLine = true,
            keyboardOptions = KeyboardOptions(
                keyboardType = KeyboardType.Uri,
                imeAction = ImeAction.Go
            ),
            keyboardActions = KeyboardActions(
                onGo = {
                    val url = processUserInput(urlText)
                    webView?.loadUrl(url)
                    keyboardController?.hide()
                }
            ),
            trailingIcon = {
                IconButton(
                    onClick = {
                        val url = processUserInput(urlText)
                        webView?.loadUrl(url)
                        keyboardController?.hide()
                    }
                ) {
                    Icon(Icons.Default.Search, contentDescription = "Go")
                }
            },
            shape = RoundedCornerShape(12.dp)
        )
        
        // Progress bar
        if (isLoading) {
            LinearProgressIndicator(
                progress = loadingProgress / 100f,
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(top = 8.dp),
                color = MaterialTheme.colorScheme.primary
            )
        }
        
        // WebView
        AndroidView(
            factory = { context ->
                WebView(context).apply {
                    setupWebView(this, context) { url, loading, progress, goBack, goForward ->
                        currentUrl = url
                        urlText = url
                        isLoading = loading
                        loadingProgress = progress
                        canGoBack = goBack
                        canGoForward = goForward
                    }
                    loadUrl(initialUrl)
                    webView = this
                    onWebViewCreated(this)
                }
            },
            modifier = Modifier
                .fillMaxSize()
                .padding(top = 8.dp)
        )
    }
}

@SuppressLint("SetJavaScriptEnabled")
private fun setupWebView(
    webView: WebView,
    context: Context,
    onStateChanged: (String, Boolean, Int, Boolean, Boolean) -> Unit
) {
    webView.settings.apply {
        javaScriptEnabled = true
        domStorageEnabled = true
        allowFileAccess = true
        allowContentAccess = true
        setSupportZoom(true)
        builtInZoomControls = true
        displayZoomControls = false
        loadWithOverviewMode = true
        useWideViewPort = true
        mixedContentMode = WebSettings.MIXED_CONTENT_COMPATIBILITY_MODE
        cacheMode = WebSettings.LOAD_DEFAULT
        
        // Enable cookies
        CookieManager.getInstance().setAcceptCookie(true)
        CookieManager.getInstance().setAcceptThirdPartyCookies(webView, true)
    }
    
    webView.webViewClient = object : WebViewClient() {
        override fun onPageStarted(view: WebView?, url: String?, favicon: Bitmap?) {
            super.onPageStarted(view, url, favicon)
            url?.let {
                onStateChanged(it, true, 0, view?.canGoBack() ?: false, view?.canGoForward() ?: false)
            }
        }
        
        override fun onPageFinished(view: WebView?, url: String?) {
            super.onPageFinished(view, url)
            url?.let {
                onStateChanged(it, false, 100, view?.canGoBack() ?: false, view?.canGoForward() ?: false)
            }
        }
        
        override fun onReceivedSslError(view: WebView?, handler: SslErrorHandler?, error: SslError?) {
            // For development purposes, you might want to proceed with SSL errors
            // In production, handle this more carefully
            handler?.proceed()
        }
        
        override fun shouldOverrideUrlLoading(view: WebView?, request: WebResourceRequest?): Boolean {
            val url = request?.url?.toString()
            Log.d("BrowserActivity", "shouldOverrideUrlLoading: $url")
            return if (url != null) {
                when {
                    // Handle special schemes that should open external apps
                    url.startsWith("tel:") || url.startsWith("mailto:") || url.startsWith("sms:") ||
                    url.startsWith("whatsapp:") || url.startsWith("telegram:") || url.startsWith("viber:") ||
                    url.startsWith("skype:") || url.startsWith("market:") || url.startsWith("play:") -> {
                        try {
                            val intent = Intent(Intent.ACTION_VIEW, Uri.parse(url))
                            context.startActivity(intent)
                            true
                        } catch (e: Exception) {
                            Toast.makeText(context, "No app found to handle this link", Toast.LENGTH_SHORT).show()
                            false
                        }
                    }
                    // Handle intent:// links
                    url.startsWith("intent://") -> {
                        try {
                            val intent = Intent.parseUri(url, Intent.URI_INTENT_SCHEME)

                            // Check if there's an app that can handle this intent
                            val packageManager = context.packageManager
                            val resolveInfo = if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.TIRAMISU) {
                                packageManager.resolveActivity(intent, PackageManager.ResolveInfoFlags.of(0))
                            } else {
                                @Suppress("DEPRECATION")
                                packageManager.resolveActivity(intent, 0)
                            }

                            if (resolveInfo != null) {
                                // App found, launch it
                                context.startActivity(intent)
                                true
                            } else {
                                // No app found, try to get fallback URL
                                val fallbackUrl = intent.getStringExtra("browser_fallback_url")
                                if (!fallbackUrl.isNullOrEmpty()) {
                                    // Load fallback URL in WebView
                                    view?.loadUrl(fallbackUrl)
                                    true
                                } else {
                                    // Try to open in Play Store if package is specified
                                    val packageName = intent.getStringExtra("package") ?: intent.`package`
                                    if (!packageName.isNullOrEmpty()) {
                                        try {
                                            val playStoreIntent = Intent(Intent.ACTION_VIEW, Uri.parse("market://details?id=$packageName"))
                                            context.startActivity(playStoreIntent)
                                            true
                                        } catch (e: Exception) {
                                            // Fallback to web Play Store
                                            val webPlayStoreIntent = Intent(Intent.ACTION_VIEW, Uri.parse("https://play.google.com/store/apps/details?id=$packageName"))
                                            context.startActivity(webPlayStoreIntent)
                                            true
                                        }
                                    } else {
                                        Toast.makeText(context, "No app found to handle this link", Toast.LENGTH_SHORT).show()
                                        false
                                    }
                                }
                            }
                        } catch (e: Exception) {
                            Toast.makeText(context, "Invalid intent link", Toast.LENGTH_SHORT).show()
                            false
                        }
                    }
                    // Handle other custom schemes (like app deep links)
                    !url.startsWith("http://") && !url.startsWith("https://") && !url.startsWith("file://") && !url.startsWith("data:") -> {
                        try {
                            val intent = Intent(Intent.ACTION_VIEW, Uri.parse(url))
                            context.startActivity(intent)
                            true
                        } catch (e: Exception) {
                            Toast.makeText(context, "No app found to handle this link", Toast.LENGTH_SHORT).show()
                            false
                        }
                    }
                    else -> false
                }
            } else {
                false
            }
        }
    }
    
    webView.webChromeClient = object : WebChromeClient() {
        override fun onProgressChanged(view: WebView?, newProgress: Int) {
            super.onProgressChanged(view, newProgress)
            val url = view?.url ?: ""
            onStateChanged(url, newProgress < 100, newProgress, view?.canGoBack() ?: false, view?.canGoForward() ?: false)
        }
        
        override fun onJsAlert(view: WebView?, url: String?, message: String?, result: JsResult?): Boolean {
            Toast.makeText(context, message, Toast.LENGTH_SHORT).show()
            result?.confirm()
            return true
        }
    }
    
    webView.setDownloadListener { url, userAgent, contentDisposition, mimeType, _ ->
        try {
            Log.d("BrowserActivity", "Download detected: $url")

            // Create intent to go back to main app with the download URL
            val intent = Intent().apply {
                putExtra("download_url", url)
                putExtra("from_browser", true)
            }

            Log.d("BrowserActivity", "Setting result with URL: $url")

            // Set result and finish to return to main app
            (context as BrowserActivity).setResult(android.app.Activity.RESULT_OK, intent)
            (context as BrowserActivity).finish()

            Log.d("BrowserActivity", "Browser activity finished")

        } catch (e: Exception) {
            Log.e("BrowserActivity", "Failed to handle download", e)
            Toast.makeText(context, "Failed to handle download: ${e.message}", Toast.LENGTH_SHORT).show()
        }
    }
}
